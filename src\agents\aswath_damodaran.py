from __future__ import annotations

import json
import numpy as np
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing_extensions import Literal
from pydantic import BaseModel

from src.graph.state import AgentState, show_agent_reasoning
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage

from src.tools.api_with_local_fallback import (
    get_financial_metrics,
    get_market_cap,
    get_current_price,
    search_line_items,
    get_prices,
    get_dynamic_market_context,
)
from src.utils.llm import call_llm
from src.utils.progress import progress


class AswathDamodaranSignal(BaseModel):
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float          # 0‒100
    reasoning: str


def aswath_damodaran_agent(state: AgentState):
    """
    Analyze US equities through Aswath Damodaran’s intrinsic‑value lens:
      • Cost of Equity via CAPM (risk‑free + β·ERP)
      • 5‑yr revenue / FCFF growth trends & reinvestment efficiency
      • FCFF‑to‑Firm DCF → equity value → per‑share intrinsic value
      • Cross‑check with relative valuation (PE vs. Fwd PE sector median proxy)
    Produces a trading signal and explanation in Damodaran’s analytical voice.
    """
    data      = state["data"]
    end_date  = data["end_date"]
    tickers   = data["tickers"]

    analysis_data: dict[str, dict] = {}
    damodaran_signals: dict[str, dict] = {}

    for ticker in tickers:
        # ─── Fetch core data ────────────────────────────────────────────────────
        progress.update_status("aswath_damodaran_agent", ticker, "Fetching financial metrics")
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5, agent_name="aswath_damodaran_agent")

        progress.update_status("aswath_damodaran_agent", ticker, "Fetching financial line items")
        line_items = search_line_items(
            ticker,
            [
                "ebit",
                "interest_expense",
                "revenue",
                "free_cash_flow",
                "ebit",
                "interest_expense",
                "capital_expenditure",
                "depreciation_and_amortization",
                "outstanding_shares",
                "net_income",
                "total_debt",
            ],
            end_date,
            agent_name="aswath_damodaran_agent"
        )

        progress.update_status("aswath_damodaran_agent", ticker, "Getting market cap")
        market_cap = get_market_cap(ticker, end_date)

        progress.update_status("aswath_damodaran_agent", ticker, "Getting current price")
        current_price = get_current_price(ticker, end_date)

        progress.update_status("aswath_damodaran_agent", ticker, "Getting dynamic market context")
        market_context = get_dynamic_market_context(ticker, end_date)

        # ─── Analyses ───────────────────────────────────────────────────────────
        progress.update_status("aswath_damodaran_agent", ticker, "Analyzing growth and reinvestment")
        growth_analysis = analyze_growth_and_reinvestment(metrics, line_items)

        progress.update_status("aswath_damodaran_agent", ticker, "Analyzing risk profile")
        risk_analysis = analyze_risk_profile(metrics, line_items, ticker, end_date)

        progress.update_status("aswath_damodaran_agent", ticker, "Calculating intrinsic value (DCF)")
        intrinsic_val_analysis = calculate_intrinsic_value_dcf(metrics, line_items, risk_analysis, market_context)

        progress.update_status("aswath_damodaran_agent", ticker, "Assessing relative valuation")
        relative_val_analysis = analyze_relative_valuation(metrics)

        # ─── Score & margin of safety ──────────────────────────────────────────
        total_score = (
            growth_analysis["score"]
            + risk_analysis["score"]
            + relative_val_analysis["score"]
        )
        max_score = growth_analysis["max_score"] + risk_analysis["max_score"] + relative_val_analysis["max_score"]

        intrinsic_value = intrinsic_val_analysis["intrinsic_value"]
        intrinsic_per_share = intrinsic_val_analysis.get("intrinsic_per_share")

        # Calculate margin of safety using per-share values for more accurate comparison
        if intrinsic_per_share and current_price:
            margin_of_safety = (intrinsic_per_share - current_price) / current_price
        elif intrinsic_value and market_cap:
            # Fallback to market cap comparison if per-share data unavailable
            margin_of_safety = (intrinsic_value - market_cap) / market_cap
        else:
            margin_of_safety = None

        # Decision rules (Damodaran tends to act with ~20‑25 % MOS)
        if margin_of_safety is not None and margin_of_safety >= 0.25:
            signal = "bullish"
        elif margin_of_safety is not None and margin_of_safety <= -0.25:
            signal = "bearish"
        else:
            signal = "neutral"

        # Enhanced decision rules incorporating market context
        base_signal = signal
        signal = adjust_signal_with_market_context(base_signal, market_context, margin_of_safety)

        # Enhanced confidence calculation incorporating market volatility
        base_confidence = min(max(abs(margin_of_safety or 0) * 200, 10), 100)
        confidence = adjust_confidence_with_market_context(base_confidence, market_context)

        analysis_data[ticker] = {
            "signal": signal,
            "confidence": confidence,
            "score": total_score,
            "max_score": max_score,
            "margin_of_safety": margin_of_safety,
            "growth_analysis": growth_analysis,
            "risk_analysis": risk_analysis,
            "relative_val_analysis": relative_val_analysis,
            "intrinsic_val_analysis": intrinsic_val_analysis,
            "market_cap": market_cap,
            "current_price": current_price,
            "intrinsic_per_share": intrinsic_per_share,
            "market_context": market_context,
        }

        # ─── LLM: craft Damodaran‑style narrative ──────────────────────────────
        progress.update_status("aswath_damodaran_agent", ticker, "Generating Damodaran analysis")
        damodaran_output = generate_damodaran_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )

        damodaran_signals[ticker] = damodaran_output.model_dump()

        progress.update_status("aswath_damodaran_agent", ticker, "Done")

    # ─── Push message back to graph state ──────────────────────────────────────
    message = HumanMessage(content=json.dumps(damodaran_signals), name="aswath_damodaran_agent")

    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(damodaran_signals, "Aswath Damodaran Agent")

    state["data"]["analyst_signals"]["aswath_damodaran_agent"] = damodaran_signals
    progress.update_status("aswath_damodaran_agent", None, "Done")

    return {"messages": [message], "data": state["data"]}


# ────────────────────────────────────────────────────────────────────────────────
# Helper analyses
# ────────────────────────────────────────────────────────────────────────────────
def analyze_growth_and_reinvestment(metrics: list, line_items: list) -> dict[str, any]:
    """
    Growth score (0‑4):
      +2  5‑yr CAGR of revenue > 8 %
      +1  5‑yr CAGR of revenue > 3 %
      +1  Positive FCFF growth over 5 yr
    Reinvestment efficiency (ROIC > WACC) adds +1
    """
    max_score = 4
    if len(metrics) < 2:
        return {"score": 0, "max_score": max_score, "details": "Insufficient history"}

    # Revenue CAGR (oldest to latest)
    # revs = [m.revenue for m in reversed(metrics) if hasattr(m, "revenue") and m.revenue]
    revs = [li.revenue for li in reversed(line_items) if hasattr(li, "revenue") and li.revenue]
    if len(revs) >= 2 and revs[0] > 0:
        cagr = (revs[-1] / revs[0]) ** (1 / (len(revs) - 1)) - 1
    else:
        cagr = None

    score, details = 0, []

    if cagr is not None:
        if cagr > 0.08:
            score += 2
            details.append(f"Revenue CAGR {cagr:.1%} (> 8 %)")
        elif cagr > 0.03:
            score += 1
            details.append(f"Revenue CAGR {cagr:.1%} (> 3 %)")
        else:
            details.append(f"Sluggish revenue CAGR {cagr:.1%}")
    else:
        details.append("Revenue data incomplete")

    # FCFF growth (proxy: free_cash_flow trend)
    fcfs = [li.free_cash_flow for li in reversed(line_items) if li.free_cash_flow]
    if len(fcfs) >= 2 and fcfs[-1] > fcfs[0]:
        score += 1
        details.append("Positive FCFF growth")
    else:
        details.append("Flat or declining FCFF")

    # Reinvestment efficiency (ROIC vs. 10 % hurdle)
    latest = metrics[0]
    if latest.return_on_invested_capital and latest.return_on_invested_capital > 0.10:
        score += 1
        details.append(f"ROIC {latest.return_on_invested_capital:.1%} (> 10 %)")

    return {"score": score, "max_score": max_score, "details": "; ".join(details), "metrics": latest.model_dump()}


def analyze_risk_profile(metrics: list, line_items: list, ticker: str | None = None, end_date: str | None = None) -> dict[str, any]:
    """
    Risk score (0‑3):
      +1  Beta < 1.3
      +1  Debt/Equity < 1
      +1  Interest Coverage > 3×
    """
    max_score = 3
    if not metrics:
        return {"score": 0, "max_score": max_score, "details": "No metrics"}

    latest = metrics[0]
    score, details = 0, []

    # Beta - try to calculate it if not available in metrics
    beta = getattr(latest, "beta", None)
    if beta is None and ticker and end_date:
        # Calculate beta using price data
        beta = calculate_beta(ticker, end_date)

    if beta is not None:
        if beta < 1.3:
            score += 1
            details.append(f"Beta {beta:.2f}")
        else:
            details.append(f"High beta {beta:.2f}")
    else:
        details.append("Beta NA")

    # Debt / Equity
    dte = getattr(latest, "debt_to_equity", None)
    if dte is not None:
        if dte < 1:
            score += 1
            details.append(f"D/E {dte:.1f}")
        else:
            details.append(f"High D/E {dte:.1f}")
    else:
        details.append("D/E NA")

    # Interest coverage
    # Get ebit and interest_expense from line_items instead of latest metrics
    latest_line_item = line_items[0] if line_items else None
    ebit = getattr(latest_line_item, "ebit", None) if latest_line_item else None
    interest = getattr(latest_line_item, "interest_expense", None) if latest_line_item else None
    if ebit and interest and interest != 0:
        coverage = ebit / abs(interest)
        if coverage > 3:
            score += 1
            details.append(f"Interest coverage × {coverage:.1f}")
        else:
            details.append(f"Weak coverage × {coverage:.1f}")
    else:
        details.append("Interest coverage NA")

    # Compute cost of equity for later use
    cost_of_equity = estimate_cost_of_equity(beta)

    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(details),
        "beta": beta,
        "cost_of_equity": cost_of_equity,
    }


def analyze_relative_valuation(metrics: list) -> dict[str, any]:
    """
    Simple PE check vs. historical median (proxy since sector comps unavailable):
      +1 if TTM P/E < 70 % of 5‑yr median
      +0 if between 70 %‑130 %
      ‑1 if >130 %
    """
    max_score = 1
    if not metrics or len(metrics) < 5:
        return {"score": 0, "max_score": max_score, "details": "Insufficient P/E history"}

    pes = [m.price_to_earnings_ratio for m in metrics if m.price_to_earnings_ratio]
    if len(pes) < 5:
        return {"score": 0, "max_score": max_score, "details": "P/E data sparse"}

    ttm_pe = pes[0]
    median_pe = sorted(pes)[len(pes) // 2]

    if ttm_pe < 0.7 * median_pe:
        score, desc = 1, f"P/E {ttm_pe:.1f} vs. median {median_pe:.1f} (cheap)"
    elif ttm_pe > 1.3 * median_pe:
        score, desc = -1, f"P/E {ttm_pe:.1f} vs. median {median_pe:.1f} (expensive)"
    else:
        score, desc = 0, f"P/E inline with history"

    return {"score": score, "max_score": max_score, "details": desc}


# ────────────────────────────────────────────────────────────────────────────────
# Intrinsic value via FCFF DCF (Damodaran style)
# ────────────────────────────────────────────────────────────────────────────────
def calculate_intrinsic_value_dcf(metrics: list, line_items: list, risk_analysis: dict, market_context: dict = None) -> dict[str, any]:
    """
    FCFF DCF with:
      • Base FCFF = latest free cash flow
      • Growth = 5‑yr revenue CAGR (capped 12 %)
      • Fade linearly to terminal growth 2.5 % by year 10
      • Discount @ cost of equity (no debt split given data limitations)
    """
    if not metrics or len(metrics) < 2 or not line_items:
        return {"intrinsic_value": None, "details": ["Insufficient data"]}

    # Get free_cash_flow from line_items instead of metrics
    latest_line_item = line_items[0] if line_items else None
    fcff0 = getattr(latest_line_item, "free_cash_flow", None) if latest_line_item else None
    shares = getattr(latest_line_item, "outstanding_shares", None) if latest_line_item else None
    if not fcff0 or not shares:
        return {"intrinsic_value": None, "details": ["Missing FCFF or share count"]}

    # Growth assumptions - get revenue from line_items instead of metrics
    revs = [li.revenue for li in reversed(line_items) if hasattr(li, 'revenue') and li.revenue]
    if len(revs) >= 2 and revs[0] > 0:
        base_growth = min((revs[-1] / revs[0]) ** (1 / (len(revs) - 1)) - 1, 0.12)
    else:
        base_growth = 0.04  # fallback

    # Market-responsive growth adjustment
    if market_context:
        momentum_20d = market_context.get("momentum_20d", {})
        momentum = momentum_20d.get("momentum", 0)
        trend = momentum_20d.get("trend", "neutral")

        # Adjust growth expectations based on market momentum
        if trend in ["strong_bullish", "bullish"] and momentum > 5:
            growth_adjustment = min(0.02, momentum / 500)  # Max 2% boost
        elif trend in ["strong_bearish", "bearish"] and momentum < -5:
            growth_adjustment = max(-0.015, momentum / 500)  # Max 1.5% reduction
        else:
            growth_adjustment = 0

        base_growth = max(0, base_growth + growth_adjustment)

    terminal_growth = 0.025
    years = 10

    # Market-responsive discount rate
    base_discount = risk_analysis.get("cost_of_equity") or 0.09

    if market_context:
        volatility = market_context.get("momentum_20d", {}).get("volatility", 0)
        # Higher volatility increases required return
        volatility_adjustment = min(0.02, volatility / 200)  # Max 2% increase
        discount = base_discount + volatility_adjustment
    else:
        discount = base_discount

    # Project FCFF and discount
    pv_sum = 0.0
    g = base_growth
    g_step = (terminal_growth - base_growth) / (years - 1)
    for yr in range(1, years + 1):
        fcff_t = fcff0 * (1 + g)
        pv = fcff_t / (1 + discount) ** yr
        pv_sum += pv
        g += g_step

    # Terminal value (perpetuity with terminal growth)
    tv = (
        fcff0
        * (1 + terminal_growth)
        / (discount - terminal_growth)
        / (1 + discount) ** years
    )

    equity_value = pv_sum + tv
    intrinsic_per_share = equity_value / shares

    # Prepare market adjustment details
    market_adjustments = {}
    if market_context:
        momentum_20d = market_context.get("momentum_20d", {})
        market_adjustments = {
            "momentum": momentum_20d.get("momentum", 0),
            "trend": momentum_20d.get("trend", "neutral"),
            "volatility": momentum_20d.get("volatility", 0),
            "growth_adjustment": base_growth - (min((revs[-1] / revs[0]) ** (1 / (len(revs) - 1)) - 1, 0.12) if len(revs) >= 2 and revs[0] > 0 else 0.04),
            "discount_adjustment": discount - base_discount,
        }

    return {
        "intrinsic_value": equity_value,
        "intrinsic_per_share": intrinsic_per_share,
        "assumptions": {
            "base_fcff": fcff0,
            "base_growth": base_growth,
            "terminal_growth": terminal_growth,
            "discount_rate": discount,
            "projection_years": years,
            "market_adjustments": market_adjustments,
        },
        "details": ["FCFF DCF completed with market-responsive adjustments"],
    }


def calculate_beta(ticker: str, end_date: str, market_ticker: str = "SPY", lookback_days: int = 252) -> float | None:
    """
    Calculate beta by comparing stock returns vs market returns over the specified period.
    If market data is unavailable, estimate beta based on stock volatility.

    Args:
        ticker: Stock ticker to calculate beta for
        end_date: End date for the calculation period
        market_ticker: Market index ticker (default: SPY for S&P 500)
        lookback_days: Number of trading days to look back (default: 252 = 1 year)

    Returns:
        Beta value or None if calculation fails
    """
    try:
        # Calculate start date
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        # Add extra days to account for weekends/holidays
        start_dt = end_dt - timedelta(days=int(lookback_days * 1.4))
        start_date = start_dt.strftime("%Y-%m-%d")

        # Get price data for stock
        stock_prices = get_prices(ticker, start_date, end_date, agent_name="aswath_damodaran_agent")

        if len(stock_prices) < 30:
            return None  # Insufficient data

        # Try to get market data
        try:
            market_prices = get_prices(market_ticker, start_date, end_date)

            if len(market_prices) >= 30:
                # Full beta calculation with market data
                return _calculate_beta_with_market_data(stock_prices, market_prices)
            else:
                # Fallback to volatility-based estimation
                return _estimate_beta_from_volatility(stock_prices)

        except Exception:
            # Market data unavailable, use volatility-based estimation
            return _estimate_beta_from_volatility(stock_prices)

    except Exception as e:
        # If calculation fails, return None
        return None


def _calculate_beta_with_market_data(stock_prices, market_prices) -> float | None:
    """Calculate beta using both stock and market price data."""
    try:
        # Convert to DataFrames
        stock_df = pd.DataFrame([p.model_dump() for p in stock_prices])
        market_df = pd.DataFrame([p.model_dump() for p in market_prices])

        # Convert time to datetime and set as index
        stock_df['time'] = pd.to_datetime(stock_df['time'])
        market_df['time'] = pd.to_datetime(market_df['time'])

        stock_df.set_index('time', inplace=True)
        market_df.set_index('time', inplace=True)

        # Calculate daily returns
        stock_df['return'] = stock_df['close'].pct_change()
        market_df['return'] = market_df['close'].pct_change()

        # Align dates and remove NaN values
        combined_df = pd.merge(stock_df[['return']], market_df[['return']],
                              left_index=True, right_index=True,
                              suffixes=('_stock', '_market'))
        combined_df.dropna(inplace=True)

        if len(combined_df) < 20:
            return None  # Insufficient overlapping data

        # Calculate beta using covariance and variance
        stock_returns = combined_df['return_stock']
        market_returns = combined_df['return_market']

        covariance = np.cov(stock_returns, market_returns)[0, 1]
        market_variance = np.var(market_returns)

        if market_variance == 0:
            return None

        beta = covariance / market_variance

        # Sanity check: beta should typically be between -3 and 3
        if abs(beta) > 5:
            return None

        return beta

    except Exception:
        return None


def _estimate_beta_from_volatility(stock_prices) -> float | None:
    """
    Estimate beta based on stock volatility when market data is unavailable.
    Uses industry-typical volatility-to-beta relationships.
    """
    try:
        # Convert to DataFrame
        stock_df = pd.DataFrame([p.model_dump() for p in stock_prices])
        stock_df['time'] = pd.to_datetime(stock_df['time'])
        stock_df.set_index('time', inplace=True)

        # Calculate daily returns
        stock_df['return'] = stock_df['close'].pct_change()
        stock_df.dropna(inplace=True)

        if len(stock_df) < 20:
            return None

        # Calculate annualized volatility
        daily_volatility = stock_df['return'].std()
        annualized_volatility = daily_volatility * np.sqrt(252)

        # Estimate beta based on volatility ranges
        # These are rough industry approximations
        if annualized_volatility < 0.15:  # Low volatility (< 15%)
            estimated_beta = 0.7
        elif annualized_volatility < 0.25:  # Medium volatility (15-25%)
            estimated_beta = 1.0
        elif annualized_volatility < 0.40:  # High volatility (25-40%)
            estimated_beta = 1.3
        else:  # Very high volatility (> 40%)
            estimated_beta = 1.8

        return estimated_beta

    except Exception:
        return None


def estimate_cost_of_equity(beta: float | None) -> float:
    """CAPM: r_e = r_f + β × ERP (use Damodaran’s long‑term averages)."""
    risk_free = 0.04          # 10‑yr US Treasury proxy
    erp = 0.05                # long‑run US equity risk premium
    beta = beta if beta is not None else 1.0
    return risk_free + beta * erp


def adjust_signal_with_market_context(base_signal: str, market_context: dict, margin_of_safety: float | None) -> str:
    """
    Adjust the base signal based on current market momentum and sentiment.
    This allows the agent to respond to short-term market dynamics while maintaining
    fundamental analysis as the primary driver.
    """
    if not market_context or "momentum_20d" not in market_context:
        return base_signal

    momentum_data = market_context["momentum_20d"]
    momentum = momentum_data.get("momentum", 0)
    trend = momentum_data.get("trend", "neutral")
    market_sentiment = market_context.get("market_sentiment", "neutral")

    # For strong fundamental signals, don't override
    if margin_of_safety is not None and abs(margin_of_safety) > 0.4:
        return base_signal

    # For marginal fundamental signals, consider market momentum
    if base_signal == "neutral":
        if trend in ["strong_bullish", "bullish"] and market_sentiment in ["very_bullish", "bullish"]:
            return "bullish"
        elif trend in ["strong_bearish", "bearish"] and market_sentiment in ["very_bearish", "bearish"]:
            return "bearish"

    # For weak fundamental signals, market momentum can provide confirmation or caution
    elif base_signal == "bullish" and trend in ["strong_bearish", "bearish"]:
        return "neutral"  # Downgrade due to negative momentum
    elif base_signal == "bearish" and trend in ["strong_bullish", "bullish"]:
        return "neutral"  # Downgrade due to positive momentum

    return base_signal


def adjust_confidence_with_market_context(base_confidence: float, market_context: dict) -> float:
    """
    Adjust confidence based on market volatility and momentum consistency.
    Higher volatility reduces confidence, while consistent momentum can increase it.
    """
    if not market_context or "momentum_20d" not in market_context:
        return base_confidence

    momentum_data = market_context["momentum_20d"]
    volatility = momentum_data.get("volatility", 0)

    # High volatility reduces confidence
    if volatility > 4:  # Very high volatility
        confidence_adjustment = -15
    elif volatility > 2:  # High volatility
        confidence_adjustment = -10
    elif volatility < 1:  # Low volatility
        confidence_adjustment = 5
    else:
        confidence_adjustment = 0

    # Check for momentum consistency across timeframes
    short_momentum = market_context.get("momentum_5d", {}).get("momentum", 0)
    long_momentum = market_context.get("momentum_60d", {}).get("momentum", 0)
    current_momentum = momentum_data.get("momentum", 0)

    # If all momentum indicators point in the same direction, increase confidence
    if (current_momentum > 1 and short_momentum > 1 and long_momentum > 1) or \
       (current_momentum < -1 and short_momentum < -1 and long_momentum < -1):
        confidence_adjustment += 10

    adjusted_confidence = base_confidence + confidence_adjustment
    return max(10, min(100, adjusted_confidence))  # Keep within 10-100 range


# ────────────────────────────────────────────────────────────────────────────────
# LLM generation
# ────────────────────────────────────────────────────────────────────────────────
def generate_damodaran_output(
    ticker: str,
    analysis_data: dict[str, any],
    model_name: str,
    model_provider: str,
) -> AswathDamodaranSignal:
    """
    Ask the LLM to channel Prof. Damodaran’s analytical style:
      • Story → Numbers → Value narrative
      • Emphasize risk, growth, and cash‑flow assumptions
      • Cite cost of capital, implied MOS, and valuation cross‑checks
    """
    template = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """You are Aswath Damodaran, Professor of Finance at NYU Stern.
                Use your valuation framework to issue trading signals on US equities.

                Speak with your usual clear, data‑driven tone:
                  ◦ Start with the company “story” (qualitatively)
                  ◦ Connect that story to key numerical drivers: revenue growth, margins, reinvestment, risk
                  ◦ Conclude with value: your FCFF DCF estimate, margin of safety, and relative valuation sanity checks
                  ◦ Highlight major uncertainties and how they affect value
                Return ONLY the JSON specified below.""",
            ),
            (
                "human",
                """Ticker: {ticker}

                Analysis data:
                {analysis_data}

                Respond EXACTLY in this JSON schema:
                {{
                  "signal": "bullish" | "bearish" | "neutral",
                  "confidence": float (0‑100),
                  "reasoning": "string"
                }}""",
            ),
        ]
    )

    prompt = template.invoke({"analysis_data": json.dumps(analysis_data, indent=2), "ticker": ticker})

    def default_signal():
        return AswathDamodaranSignal(
            signal="neutral",
            confidence=0.0,
            reasoning="Parsing error; defaulting to neutral",
        )

    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=AswathDamodaranSignal,
        agent_name="aswath_damodaran_agent",
        default_factory=default_signal,
    )
