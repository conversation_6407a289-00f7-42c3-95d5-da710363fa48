from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.tools.api_with_local_fallback import get_financial_metrics, search_line_items, get_insider_trades
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any
import json


class FundamentalsAnalysisSignal(BaseModel):
    """Pydantic model for fundamentals analysis LLM output"""
    signal: str = Field(description="Trading signal: 'bullish', 'bearish', or 'neutral'")
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    profitability_assessment: str = Field(description="Assessment of company's profitability")
    growth_assessment: str = Field(description="Assessment of company's growth prospects")
    financial_health_assessment: str = Field(description="Assessment of company's financial health")
    valuation_assessment: str = Field(description="Assessment of company's valuation")
    insider_activity_assessment: str = Field(description="Assessment of insider trading activity")


##### Fundamentals Analyst Agent #####
def fundamentals_analyst_agent(state: AgentState):
    """Analyzes comprehensive fundamental data using LLM to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    model_name = state["metadata"]["model_name"]
    model_provider = state["metadata"]["model_provider"]

    # Initialize fundamentals analysis for each ticker
    fundamentals_analysis = {}

    for ticker in tickers:
        progress.update_status("fundamentals_analyst_agent", ticker, "Fetching financial metrics")

        # Get comprehensive financial metrics
        financial_metrics = get_financial_metrics(
            ticker=ticker,
            end_date=end_date,
            period="ttm",
            limit=10,
            agent_name="fundamentals_analyst_agent",
        )

        # Get additional line items for detailed analysis
        line_items = search_line_items(
            ticker=ticker,
            line_items=["revenue", "net_income", "total_assets", "total_debt", "free_cash_flow"],
            end_date=end_date,
            period="ttm",
            limit=5,
            agent_name="fundamentals_analyst_agent",
        )

        # Get insider trading data
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=10,
            agent_name="fundamentals_analyst_agent",
        )

        if not financial_metrics:
            progress.update_status("fundamentals_analyst_agent", ticker, "Failed: No financial metrics found")
            continue

        # Prepare comprehensive financial data for LLM analysis
        financial_data = {}

        # Extract the most recent financial metrics
        if financial_metrics:
            metrics = financial_metrics[0]
            financial_data["financial_metrics"] = {
                "return_on_equity": getattr(metrics, 'return_on_equity', None),
                "return_on_assets": getattr(metrics, 'return_on_assets', None),
                "net_margin": getattr(metrics, 'net_margin', None),
                "operating_margin": getattr(metrics, 'operating_margin', None),
                "gross_margin": getattr(metrics, 'gross_margin', None),
                "revenue_growth": getattr(metrics, 'revenue_growth', None),
                "earnings_growth": getattr(metrics, 'earnings_growth', None),
                "book_value_growth": getattr(metrics, 'book_value_growth', None),
                "current_ratio": getattr(metrics, 'current_ratio', None),
                "debt_to_equity": getattr(metrics, 'debt_to_equity', None),
                "free_cash_flow_per_share": getattr(metrics, 'free_cash_flow_per_share', None),
                "earnings_per_share": getattr(metrics, 'earnings_per_share', None),
                "price_to_earnings_ratio": getattr(metrics, 'price_to_earnings_ratio', None),
                "price_to_book_ratio": getattr(metrics, 'price_to_book_ratio', None),
                "price_to_sales_ratio": getattr(metrics, 'price_to_sales_ratio', None),
                "peg_ratio": getattr(metrics, 'peg_ratio', None),
            }

        # Add line items data
        if line_items:
            financial_data["line_items"] = []
            for item in line_items[:5]:  # Limit to recent 5 entries
                financial_data["line_items"].append({
                    "date": getattr(item, 'date', None),
                    "total_revenue": getattr(item, 'total_revenue', None),
                    "net_income": getattr(item, 'net_income', None),
                    "total_assets": getattr(item, 'total_assets', None),
                    "total_debt": getattr(item, 'total_debt', None),
                    "free_cash_flow": getattr(item, 'free_cash_flow', None),
                })

        # Add insider trading data
        if insider_trades:
            financial_data["insider_trades"] = []
            for trade in insider_trades[:5]:  # Limit to recent 5 trades
                financial_data["insider_trades"].append({
                    "date": getattr(trade, 'date', None),
                    "transaction_shares": getattr(trade, 'transaction_shares', None),
                    "transaction_type": getattr(trade, 'transaction_type', None),
                    "insider_name": getattr(trade, 'insider_name', None),
                })

        # Create LLM prompt for comprehensive fundamentals analysis
        template = ChatPromptTemplate.from_messages([
            ("system", """You are an expert fundamental analyst with deep knowledge of financial statement analysis, valuation methods, and investment principles. Your task is to analyze the provided financial data for {ticker} and generate a comprehensive investment recommendation.

Analyze the following aspects in detail:

1. **Profitability Analysis**: Examine ROE, ROA, profit margins, and overall earning power
2. **Growth Analysis**: Evaluate revenue growth, earnings growth, and future growth prospects
3. **Financial Health**: Assess liquidity, debt levels, cash flow, and financial stability
4. **Valuation Analysis**: Determine if the stock is fairly valued using P/E, P/B, P/S, PEG ratios
5. **Insider Activity**: Interpret insider trading patterns and their implications

Consider the following financial data:
{financial_data}

You must respond with a JSON object containing exactly these fields:
- "signal": must be one of "bullish", "bearish", or "neutral"
- "confidence": a number between 0 and 100 representing your confidence level
- "reasoning": detailed explanation of your decision
- "profitability_assessment": assessment of company's profitability
- "growth_assessment": assessment of company's growth prospects
- "financial_health_assessment": assessment of company's financial health
- "valuation_assessment": assessment of company's valuation
- "insider_activity_assessment": assessment of insider activity implications

Be thorough but concise. Focus on the most important factors that drive your investment decision.

Respond ONLY with the JSON object, no additional text."""),
            ("user", "Please analyze the fundamental data for {ticker} and provide your investment recommendation as a JSON object.")
        ])

        prompt = template.invoke({
            "ticker": ticker,
            "financial_data": json.dumps(financial_data, indent=2, default=str)
        })

        progress.update_status("fundamentals_analyst_agent", ticker, "Getting LLM analysis")

        # Call LLM for analysis
        def create_default_fundamentals_signal():
            return FundamentalsAnalysisSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in LLM analysis; defaulting to neutral",
                profitability_assessment="Unable to assess profitability",
                growth_assessment="Unable to assess growth",
                financial_health_assessment="Unable to assess financial health",
                valuation_assessment="Unable to assess valuation",
                insider_activity_assessment="Unable to assess insider activity"
            )

        llm_result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=FundamentalsAnalysisSignal,
            agent_name="fundamentals_analyst_agent",
            default_factory=create_default_fundamentals_signal,
        )

        progress.update_status("fundamentals_analyst_agent", ticker, "Processing LLM results")

        # Process LLM results and create structured reasoning
        reasoning = {
            "profitability_signal": {
                "signal": llm_result.signal,
                "details": llm_result.profitability_assessment,
            },
            "growth_signal": {
                "signal": llm_result.signal,
                "details": llm_result.growth_assessment,
            },
            "financial_health_signal": {
                "signal": llm_result.signal,
                "details": llm_result.financial_health_assessment,
            },
            "valuation_signal": {
                "signal": llm_result.signal,
                "details": llm_result.valuation_assessment,
            },
            "insider_signal": {
                "signal": llm_result.signal,
                "details": llm_result.insider_activity_assessment,
            },
        }

        # Store the analysis results
        fundamentals_analysis[ticker] = {
            "signal": llm_result.signal,
            "confidence": llm_result.confidence,
            "reasoning": reasoning,
        }

        progress.update_status("fundamentals_analyst_agent", ticker, "Done")

    # Create the fundamentals analysis message
    message = HumanMessage(
        content=json.dumps(fundamentals_analysis),
        name="fundamentals_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(fundamentals_analysis, "Fundamentals Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["fundamentals_analyst_agent"] = fundamentals_analysis

    progress.update_status("fundamentals_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
