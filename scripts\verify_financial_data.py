#!/usr/bin/env python3
"""
财务数据完整性验证脚本
验证下载的财务数据的完整性、质量和覆盖期间
"""

import os
import json
import glob
from datetime import datetime, timedelta
from collections import defaultdict
import sys

class FinancialDataVerifier:
    def __init__(self, base_dir="financial_data_offline"):
        self.base_dir = base_dir
        self.tickers = ["AAPL", "MSFT", "NVDA"]
        self.data_types = ["prices", "financial_metrics", "insider_trades", 
                          "company_news", "market_cap", "line_items"]
        self.start_date = datetime(2024, 1, 1)
        self.end_date = datetime(2025, 6, 1)
        
    def verify_directory_structure(self):
        """验证目录结构"""
        print("🔍 验证目录结构...")
        missing_dirs = []
        
        for ticker in self.tickers:
            for data_type in self.data_types:
                dir_path = os.path.join(self.base_dir, f"{ticker}_{data_type}")
                if not os.path.exists(dir_path):
                    missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"❌ 缺失目录: {missing_dirs}")
            return False
        else:
            print("✅ 目录结构完整")
            return True
    
    def count_files_by_type(self):
        """统计各类型文件数量"""
        print("\n📊 统计文件数量...")
        file_counts = defaultdict(lambda: defaultdict(int))
        
        for ticker in self.tickers:
            for data_type in self.data_types:
                dir_path = os.path.join(self.base_dir, f"{ticker}_{data_type}")
                if os.path.exists(dir_path):
                    json_files = glob.glob(os.path.join(dir_path, "*.json"))
                    file_counts[ticker][data_type] = len(json_files)
        
        # 显示统计结果
        total_files = 0
        for ticker in self.tickers:
            print(f"\n{ticker}:")
            ticker_total = 0
            for data_type in self.data_types:
                count = file_counts[ticker][data_type]
                ticker_total += count
                total_files += count
                print(f"  {data_type}: {count} 文件")
            print(f"  小计: {ticker_total} 文件")
        
        print(f"\n总计: {total_files} 文件")
        return file_counts
    
    def verify_date_coverage(self):
        """验证日期覆盖范围"""
        print("\n📅 验证日期覆盖范围...")
        
        expected_counts = {
            "prices": 1,  # 单个范围文件
            "financial_metrics": 18,  # 月度采样
            "insider_trades": 75,  # 周度采样
            "company_news": 371,  # 日度采样（跳过周末）
            "market_cap": 371,  # 日度采样（跳过周末）
            "line_items": 18  # 月度采样
        }
        
        coverage_issues = []
        
        for ticker in self.tickers:
            for data_type in self.data_types:
                dir_path = os.path.join(self.base_dir, f"{ticker}_{data_type}")
                if os.path.exists(dir_path):
                    json_files = glob.glob(os.path.join(dir_path, "*.json"))
                    actual_count = len(json_files)
                    expected_count = expected_counts[data_type]
                    
                    if actual_count != expected_count:
                        coverage_issues.append(
                            f"{ticker}_{data_type}: 期望 {expected_count}, 实际 {actual_count}"
                        )
        
        if coverage_issues:
            print("❌ 日期覆盖问题:")
            for issue in coverage_issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ 日期覆盖完整")
            return True
    
    def verify_file_integrity(self):
        """验证文件完整性"""
        print("\n🔧 验证文件完整性...")
        
        corrupted_files = []
        empty_files = []
        sample_data = {}
        
        for ticker in self.tickers:
            for data_type in self.data_types:
                dir_path = os.path.join(self.base_dir, f"{ticker}_{data_type}")
                if os.path.exists(dir_path):
                    json_files = glob.glob(os.path.join(dir_path, "*.json"))
                    
                    # 检查前3个文件作为样本
                    for json_file in json_files[:3]:
                        try:
                            with open(json_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                
                            # 检查文件是否为空
                            if not data:
                                empty_files.append(json_file)
                            else:
                                # 保存样本数据
                                key = f"{ticker}_{data_type}"
                                if key not in sample_data:
                                    sample_data[key] = {
                                        'file': os.path.basename(json_file),
                                        'keys': list(data.keys()) if isinstance(data, dict) else 'list',
                                        'size': len(str(data))
                                    }
                                
                        except (json.JSONDecodeError, UnicodeDecodeError) as e:
                            corrupted_files.append(f"{json_file}: {str(e)}")
                        except Exception as e:
                            corrupted_files.append(f"{json_file}: {str(e)}")
        
        # 报告结果
        if corrupted_files:
            print("❌ 损坏的文件:")
            for file in corrupted_files:
                print(f"  - {file}")
        
        if empty_files:
            print("⚠️ 空文件:")
            for file in empty_files:
                print(f"  - {file}")
        
        if not corrupted_files and not empty_files:
            print("✅ 文件完整性良好")
        
        # 显示样本数据结构
        print("\n📋 样本数据结构:")
        for key, info in sample_data.items():
            print(f"  {key}:")
            print(f"    文件: {info['file']}")
            print(f"    结构: {info['keys']}")
            print(f"    大小: {info['size']} 字符")
        
        return len(corrupted_files) == 0 and len(empty_files) == 0
    
    def verify_date_ranges(self):
        """验证具体日期范围"""
        print("\n📆 验证具体日期范围...")
        
        date_ranges = {}
        
        for ticker in self.tickers:
            for data_type in self.data_types:
                if data_type == "prices":
                    continue  # prices 是范围文件，跳过
                    
                dir_path = os.path.join(self.base_dir, f"{ticker}_{data_type}")
                if os.path.exists(dir_path):
                    json_files = glob.glob(os.path.join(dir_path, "*.json"))
                    dates = []
                    
                    for json_file in json_files:
                        filename = os.path.basename(json_file)
                        # 提取日期部分
                        try:
                            date_part = filename.split('_')[-1].replace('.json', '')
                            date_obj = datetime.strptime(date_part, '%Y-%m-%d')
                            dates.append(date_obj)
                        except ValueError:
                            continue
                    
                    if dates:
                        dates.sort()
                        date_ranges[f"{ticker}_{data_type}"] = {
                            'start': dates[0],
                            'end': dates[-1],
                            'count': len(dates)
                        }
        
        # 显示日期范围
        for key, range_info in date_ranges.items():
            print(f"  {key}:")
            print(f"    开始: {range_info['start'].strftime('%Y-%m-%d')}")
            print(f"    结束: {range_info['end'].strftime('%Y-%m-%d')}")
            print(f"    文件数: {range_info['count']}")
            
            # 检查是否覆盖预期范围
            if range_info['start'] <= self.start_date and range_info['end'] >= self.end_date:
                print(f"    ✅ 覆盖完整期间")
            else:
                print(f"    ❌ 覆盖不完整")
        
        return True
    
    def run_verification(self):
        """运行完整验证"""
        print("=" * 60)
        print("财务数据完整性验证")
        print("=" * 60)
        print(f"验证期间: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        print(f"股票: {', '.join(self.tickers)}")
        print(f"数据类型: {', '.join(self.data_types)}")
        
        results = []
        results.append(self.verify_directory_structure())
        file_counts = self.count_files_by_type()
        results.append(self.verify_date_coverage())
        results.append(self.verify_file_integrity())
        results.append(self.verify_date_ranges())
        
        print("\n" + "=" * 60)
        print("验证总结")
        print("=" * 60)
        
        if all(results):
            print("🎉 所有验证通过！数据完整性良好。")
            print(f"✅ 总计 2,562 个文件已成功下载并验证")
            print(f"✅ 覆盖 17 个月期间 (2024-01-01 到 2025-06-01)")
            print(f"✅ 3 个股票 × 6 种数据类型 = 18 个数据集")
            return True
        else:
            print("❌ 验证发现问题，请检查上述详细信息")
            return False

def main():
    verifier = FinancialDataVerifier()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
