from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.tools.api_with_local_fallback import get_prices
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any
import pandas as pd
import numpy as np
import json


class MarketAnalysisSignal(BaseModel):
    """Pydantic model for market analysis LLM output"""
    signal: str = Field(description="Trading signal: 'bullish', 'bearish', or 'neutral'")
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    trend_analysis: str = Field(description="Analysis of price trends and moving averages")
    momentum_analysis: str = Field(description="Analysis of momentum indicators like RSI and MACD")
    volume_analysis: str = Field(description="Analysis of volume patterns and trends")
    support_resistance_analysis: str = Field(description="Analysis of support and resistance levels")
    technical_pattern_analysis: str = Field(description="Analysis of technical patterns and formations")


##### Market Analyst Agent #####
def market_analyst_agent(state: AgentState):
    """Analyzes market and technical indicators using LLM to generate trading signals for multiple tickers."""
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    model_name = state["metadata"]["model_name"]
    model_provider = state["metadata"]["model_provider"]

    # Initialize market analysis for each ticker
    market_analysis = {}

    for ticker in tickers:
        progress.update_status("market_analyst_agent", ticker, "Fetching price data")

        # Get price data for technical analysis (1 year of data)
        from datetime import datetime, timedelta
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=365)  # Get 1 year of data for technical indicators
        start_date = start_dt.strftime("%Y-%m-%d")

        price_data = get_prices(
            ticker=ticker,
            start_date=start_date,
            end_date=end_date,
            agent_name="market_analyst_agent",
        )

        if not price_data or len(price_data) < 50:
            progress.update_status("market_analyst_agent", ticker, "Failed: Insufficient price data")
            continue

        # Convert to DataFrame for analysis
        df = pd.DataFrame([{
            'date': p.time,
            'open': p.open,
            'high': p.high,
            'low': p.low,
            'close': p.close,
            'volume': p.volume
        } for p in price_data])
        
        df = df.sort_values('date').reset_index(drop=True)

        progress.update_status("market_analyst_agent", ticker, "Preparing technical data for LLM analysis")

        # Calculate technical indicators for LLM analysis (without hardcoded thresholds)
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['ema_10'] = df['close'].ewm(span=10).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()

        # Calculate RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # Calculate MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']

        # Calculate volume indicators
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']

        # Prepare technical data for LLM analysis
        recent_data = df.tail(30)  # Last 30 days for analysis
        current_data = df.iloc[-1]

        technical_data = {
            "current_price": float(current_data['close']),
            "price_change_1d": float((current_data['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close'] * 100) if len(df) > 1 else 0,
            "price_change_5d": float((current_data['close'] - df.iloc[-6]['close']) / df.iloc[-6]['close'] * 100) if len(df) > 5 else 0,
            "price_change_20d": float((current_data['close'] - df.iloc[-21]['close']) / df.iloc[-21]['close'] * 100) if len(df) > 20 else 0,
            "moving_averages": {
                "sma_20": float(current_data['sma_20']) if not pd.isna(current_data['sma_20']) else None,
                "sma_50": float(current_data['sma_50']) if not pd.isna(current_data['sma_50']) else None,
                "ema_10": float(current_data['ema_10']) if not pd.isna(current_data['ema_10']) else None,
            },
            "momentum_indicators": {
                "rsi": float(current_data['rsi']) if not pd.isna(current_data['rsi']) else None,
                "macd": float(current_data['macd']) if not pd.isna(current_data['macd']) else None,
                "macd_signal": float(current_data['macd_signal']) if not pd.isna(current_data['macd_signal']) else None,
                "macd_histogram": float(current_data['macd_histogram']) if not pd.isna(current_data['macd_histogram']) else None,
            },
            "volume_analysis": {
                "current_volume": float(current_data['volume']),
                "avg_volume_20d": float(current_data['volume_sma']) if not pd.isna(current_data['volume_sma']) else None,
                "volume_ratio": float(current_data['volume_ratio']) if not pd.isna(current_data['volume_ratio']) else None,
            },
            "price_levels": {
                "high_52w": float(df['high'].rolling(window=min(252, len(df))).max().iloc[-1]),
                "low_52w": float(df['low'].rolling(window=min(252, len(df))).min().iloc[-1]),
                "high_20d": float(recent_data['high'].max()),
                "low_20d": float(recent_data['low'].min()),
            }
        }

        # Create LLM prompt for comprehensive market analysis
        template = ChatPromptTemplate.from_messages([
            ("system", """You are an expert technical analyst with deep knowledge of market analysis, chart patterns, and technical indicators. Your task is to analyze the provided technical data for {ticker} and generate a comprehensive trading recommendation.

Analyze the following aspects in detail:

1. **Trend Analysis**: Examine price trends, moving averages, and overall market direction
2. **Momentum Analysis**: Evaluate RSI, MACD, and other momentum indicators for overbought/oversold conditions
3. **Volume Analysis**: Assess volume patterns, volume-price relationships, and institutional activity
4. **Support/Resistance Analysis**: Identify key price levels, breakouts, and potential reversals
5. **Technical Pattern Analysis**: Look for chart patterns, formations, and technical setups

Consider the following technical data:
{technical_data}

Based on your analysis, provide a JSON response with the following structure:
{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0-100,
  "reasoning": "detailed reasoning explaining your decision",
  "trend_analysis": "analysis of price trends and moving averages",
  "momentum_analysis": "analysis of momentum indicators like RSI and MACD",
  "volume_analysis": "analysis of volume patterns and trends",
  "support_resistance_analysis": "analysis of support and resistance levels",
  "technical_pattern_analysis": "analysis of technical patterns and formations"
}}

Focus on the most important technical factors that drive your trading decision. Consider both short-term and medium-term perspectives."""),
            ("user", "Please analyze the technical data for {ticker} and provide your trading recommendation in JSON format.")
        ])

        prompt = template.invoke({
            "ticker": ticker,
            "technical_data": json.dumps(technical_data, indent=2, default=str)
        })

        progress.update_status("market_analyst_agent", ticker, "Getting LLM technical analysis")

        # Call LLM for analysis
        def create_default_market_signal():
            return MarketAnalysisSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in LLM analysis; defaulting to neutral",
                trend_analysis="Unable to assess trend",
                momentum_analysis="Unable to assess momentum",
                volume_analysis="Unable to assess volume",
                support_resistance_analysis="Unable to assess support/resistance",
                technical_pattern_analysis="Unable to assess technical patterns"
            )

        llm_result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=MarketAnalysisSignal,
            agent_name="market_analyst_agent",
            default_factory=create_default_market_signal,
        )

        progress.update_status("market_analyst_agent", ticker, "Processing LLM results")

        # Process LLM results and create structured reasoning
        reasoning = {
            "trend_signal": {
                "signal": llm_result.signal,
                "details": llm_result.trend_analysis,
            },
            "momentum_signal": {
                "signal": llm_result.signal,
                "details": llm_result.momentum_analysis,
            },
            "volume_signal": {
                "signal": llm_result.signal,
                "details": llm_result.volume_analysis,
            },
            "support_resistance_signal": {
                "signal": llm_result.signal,
                "details": llm_result.support_resistance_analysis,
            },
            "technical_pattern_signal": {
                "signal": llm_result.signal,
                "details": llm_result.technical_pattern_analysis,
            },
        }

        # Store the analysis results
        market_analysis[ticker] = {
            "signal": llm_result.signal,
            "confidence": llm_result.confidence,
            "reasoning": reasoning,
        }

        progress.update_status("market_analyst_agent", ticker, "Done")

    # Create the market analysis message
    message = HumanMessage(
        content=json.dumps(market_analysis),
        name="market_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(market_analysis, "Market Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["market_analyst_agent"] = market_analysis

    progress.update_status("market_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
